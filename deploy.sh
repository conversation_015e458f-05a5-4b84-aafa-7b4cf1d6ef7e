#!/bin/bash

# AWS Lambda Deployment Script for Family Therapy PIE Notes Generator

set -e  # Exit on any error

echo "🚀 Starting Lambda deployment package creation..."

# Configuration
PACKAGE_NAME="family-therapy-lambda"
DEPLOYMENT_DIR="deployment"
ZIP_FILE="${PACKAGE_NAME}.zip"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is required but not installed."
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    print_error "pip3 is required but not installed."
    exit 1
fi

print_status "Python and pip found"

# Create deployment directory
if [ -d "$DEPLOYMENT_DIR" ]; then
    print_warning "Deployment directory exists, cleaning up..."
    rm -rf "$DEPLOYMENT_DIR"
fi

mkdir -p "$DEPLOYMENT_DIR"
print_status "Created deployment directory: $DEPLOYMENT_DIR"

# Copy Lambda function
cp lambda_function.py "$DEPLOYMENT_DIR/"
print_status "Copied lambda_function.py"

# Install dependencies
print_status "Installing Python dependencies..."
pip3 install -r requirements.txt -t "$DEPLOYMENT_DIR/" --no-deps

# Remove unnecessary files to reduce package size
print_status "Cleaning up unnecessary files..."
find "$DEPLOYMENT_DIR" -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find "$DEPLOYMENT_DIR" -type f -name "*.pyc" -delete 2>/dev/null || true
find "$DEPLOYMENT_DIR" -type f -name "*.pyo" -delete 2>/dev/null || true
find "$DEPLOYMENT_DIR" -type d -name "*.dist-info" -exec rm -rf {} + 2>/dev/null || true
find "$DEPLOYMENT_DIR" -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true

# Create ZIP file
print_status "Creating deployment package..."
cd "$DEPLOYMENT_DIR"
zip -r "../$ZIP_FILE" . -q
cd ..

# Check file size
FILE_SIZE=$(du -h "$ZIP_FILE" | cut -f1)
print_status "Deployment package created: $ZIP_FILE ($FILE_SIZE)"

# Validate package size (Lambda limit is 50MB for direct upload)
FILE_SIZE_BYTES=$(stat -f%z "$ZIP_FILE" 2>/dev/null || stat -c%s "$ZIP_FILE" 2>/dev/null)
MAX_SIZE_BYTES=$((50 * 1024 * 1024))  # 50MB

if [ "$FILE_SIZE_BYTES" -gt "$MAX_SIZE_BYTES" ]; then
    print_warning "Package size ($FILE_SIZE) exceeds 50MB limit for direct upload"
    print_warning "You'll need to upload via S3 or reduce package size"
else
    print_status "Package size is within Lambda limits"
fi

# Clean up deployment directory
rm -rf "$DEPLOYMENT_DIR"
print_status "Cleaned up temporary files"

echo ""
echo "🎉 Deployment package ready!"
echo "📦 File: $ZIP_FILE"
echo "📏 Size: $FILE_SIZE"
echo ""
echo "Next steps:"
echo "1. Upload $ZIP_FILE to AWS Lambda"
echo "2. Set environment variable: OPENAI_API_KEY"
echo "3. Configure timeout to at least 2 minutes"
echo "4. Set memory to at least 512MB"
echo ""
print_status "Deployment package creation completed successfully!"
