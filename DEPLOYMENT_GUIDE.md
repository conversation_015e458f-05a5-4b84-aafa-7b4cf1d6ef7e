# AWS Lambda Deployment Guide

## Hướng dẫn triển khai Lambda Function cho Family Therapy PIE Notes Generator

### 1. <PERSON><PERSON><PERSON> bị

✅ **Đã hoàn thành:**
- [x] Code Lambda function (`lambda_function.py`)
- [x] Dependencies (`requirements.txt`)
- [x] Test script (`test_lambda.py`)
- [x] Deployment package (`family-therapy-lambda.zip`)

### 2. Tạo Lambda Function trên AWS

#### Bước 1: Đăng nhập AWS Console
1. T<PERSON><PERSON> cập [AWS Console](https://console.aws.amazon.com/)
2. Chọn region phù hợp (ví dụ: us-east-1)

#### Bước 2: Tạo Lambda Function
1. Vào service **Lambda**
2. Click **Create function**
3. Chọn **Author from scratch**
4. Cấu hình:
   - **Function name**: `family-therapy-pie-notes`
   - **Runtime**: `Python 3.8` hoặc `Python 3.9`
   - **Architecture**: `x86_64`

#### Bước 3: Upload Code
1. Trong function vừa tạo, vào tab **Code**
2. Click **Upload from** → **.zip file**
3. Upload file `family-therapy-lambda.zip`
4. Click **Save**

### 3. Cấu hình Lambda Function

#### Environment Variables
Vào tab **Configuration** → **Environment variables**:
```
OPENAI_API_KEY = your_openai_api_key_here
```

#### Timeout & Memory
Vào tab **Configuration** → **General configuration**:
- **Timeout**: `2 minutes 0 seconds`
- **Memory**: `512 MB` (hoặc cao hơn)

#### Execution Role
Đảm bảo Lambda có quyền:
- `AWSLambdaBasicExecutionRole` (để ghi logs)

### 4. Test Function

#### Test Event
Tạo test event với JSON sau:
```json
{
  "transcript": "therapist: Good morning, how are you both feeling today?\nclient1: I've been feeling really anxious about our communication issues.\nclient2: I agree, we seem to argue about everything lately.\ntherapist: Let's explore what's happening in your communication patterns.",
  "target_client_label": "client1",
  "diagnosis_info": "Generalized Anxiety Disorder"
}
```

#### Chạy Test
1. Click **Test**
2. Kiểm tra response có format đúng
3. Xem logs trong CloudWatch

### 5. API Gateway (Tùy chọn)

Nếu muốn tạo REST API:

#### Tạo API Gateway
1. Vào service **API Gateway**
2. Tạo **REST API**
3. Tạo resource và method POST
4. Integrate với Lambda function

#### CORS Configuration
```json
{
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "Content-Type",
  "Access-Control-Allow-Methods": "POST, OPTIONS"
}
```

### 6. Monitoring & Logs

#### CloudWatch Logs
- Logs được tự động ghi vào CloudWatch
- Log group: `/aws/lambda/family-therapy-pie-notes`

#### Metrics
Monitor:
- Invocations
- Duration
- Errors
- Throttles

### 7. Cost Optimization

#### Pricing Factors
- **Requests**: $0.20 per 1M requests
- **Duration**: $0.0000166667 per GB-second
- **OpenAI API**: Theo usage của GPT-4

#### Optimization Tips
1. Optimize memory allocation
2. Use provisioned concurrency nếu cần
3. Monitor OpenAI API usage

### 8. Security Best Practices

#### API Key Management
- Sử dụng AWS Secrets Manager thay vì environment variables
- Rotate API keys định kỳ

#### Network Security
- Sử dụng VPC nếu cần
- Restrict outbound connections

#### IAM Permissions
- Principle of least privilege
- Separate roles cho dev/prod

### 9. Troubleshooting

#### Common Issues

**Error: "OPENAI_API_KEY environment variable is not set"**
- Kiểm tra environment variables
- Đảm bảo API key đúng format

**Timeout Error**
- Tăng timeout setting
- Optimize code performance
- Check OpenAI API response time

**Memory Error**
- Tăng memory allocation
- Monitor memory usage

**Rate Limit Error**
- Implement exponential backoff
- Check OpenAI rate limits
- Consider upgrading OpenAI plan

### 10. Next Steps

1. **Production Deployment**
   - Tạo separate environments (dev/staging/prod)
   - Implement CI/CD pipeline
   - Add comprehensive monitoring

2. **Enhanced Features**
   - Add input validation
   - Implement caching
   - Add batch processing

3. **Integration**
   - Connect với frontend application
   - Add authentication/authorization
   - Implement audit logging

---

## Support

Nếu gặp vấn đề, kiểm tra:
1. CloudWatch Logs
2. Lambda metrics
3. OpenAI API status
4. Network connectivity
