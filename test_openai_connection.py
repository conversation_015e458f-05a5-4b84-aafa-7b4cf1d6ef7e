#!/usr/bin/env python3
"""
Test script to check OpenAI API connection and troubleshoot issues
"""
import os
import requests
import json

def test_openai_api():
    """Test OpenAI API connection"""
    
    API_KEY = os.environ.get('OPENAI_API_KEY')
    API_URL = "https://api.openai.com/v1/chat/completions"
    
    print("🔍 Testing OpenAI API Connection...")
    print("="*50)
    
    # Check API key
    if not API_KEY:
        print("❌ OPENAI_API_KEY environment variable is not set")
        print("Please set it with: export OPENAI_API_KEY='your-api-key-here'")
        return False
    
    if not API_KEY.startswith('sk-'):
        print(f"⚠️  API key format looks suspicious: {API_KEY[:10]}...")
        print("OpenAI API keys should start with 'sk-'")
        return False
    
    print(f"✅ API key found: {API_KEY[:10]}...{API_KEY[-4:]}")
    
    # Test API call
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}"
    }
    
    # Simple test payload
    payload = {
        "model": "gpt-4o-mini",
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Say 'Hello, this is a test!'"}
        ],
        "temperature": 0.5,
        "max_tokens": 50
    }
    
    print("\n🚀 Making test API call...")
    print(f"URL: {API_URL}")
    print(f"Model: {payload['model']}")
    
    try:
        response = requests.post(API_URL, headers=headers, json=payload, timeout=30)
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            content = response_data["choices"][0]["message"]["content"]
            print(f"✅ API call successful!")
            print(f"📝 Response: {content}")
            return True
        else:
            print(f"❌ API call failed with status {response.status_code}")
            print(f"📝 Error response: {response.text}")
            
            # Common error codes
            if response.status_code == 401:
                print("💡 This is an authentication error. Check your API key.")
            elif response.status_code == 429:
                print("💡 Rate limit exceeded. Wait a moment and try again.")
            elif response.status_code == 500:
                print("💡 OpenAI server error. This might be temporary.")
            elif response.status_code == 503:
                print("💡 Service unavailable. OpenAI might be experiencing issues.")
            
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - check your internet connection")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def test_model_availability():
    """Test if the specific model is available"""
    
    API_KEY = os.environ.get('OPENAI_API_KEY')
    if not API_KEY:
        print("❌ No API key available for model test")
        return
    
    print("\n🔍 Testing model availability...")
    
    models_to_test = ["gpt-4o-mini", "gpt-4", "gpt-3.5-turbo"]
    
    for model in models_to_test:
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {API_KEY}"
        }
        
        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": "Hi"}
            ],
            "max_tokens": 5
        }
        
        try:
            response = requests.post("https://api.openai.com/v1/chat/completions", 
                                   headers=headers, json=payload, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {model}: Available")
            else:
                print(f"❌ {model}: Not available (status: {response.status_code})")
                
        except Exception as e:
            print(f"❌ {model}: Error - {str(e)}")

if __name__ == "__main__":
    success = test_openai_api()
    
    if success:
        test_model_availability()
    
    print("\n" + "="*50)
    if success:
        print("🎉 OpenAI API connection test completed successfully!")
        print("Your Lambda function should work with the OpenAI API.")
    else:
        print("❌ OpenAI API connection test failed.")
        print("Please fix the issues above before deploying to Lambda.")
