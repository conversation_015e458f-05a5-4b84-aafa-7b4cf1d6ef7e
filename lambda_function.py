import datetime
import json
import os
import requests
from typing import Dict, Optional, Any, List, Tuple
# import tiktoken  # Removed due to binary compatibility issues on Lambda
import time
import concurrent.futures
from requests.adapters import HTT<PERSON>dapter
from urllib3.util.retry import Retry
import asyncio
from asyncio import Semaphore

# Get API key from environment variable
API_KEY = os.environ.get('OPENAI_API_KEY')
API_URL = "https://api.openai.com/v1/chat/completions"

# Constants for token management
MAX_CONTEXT_LENGTH = 32000  # Maximum context length for GPT-4-turbo-preview
MIN_COMPLETION_TOKENS = 1000  # Minimum tokens for completion
MAX_COMPLETION_TOKENS = 4000  # Maximum tokens for completion
SYSTEM_PROMPT_TOKENS = 100    # Approximate tokens for system prompt
SAFETY_BUFFER = 1000         # Safety buffer for prompt overhead
MAX_TRANSCRIPT_TOKENS = MAX_CONTEXT_LENGTH - MIN_COMPLETION_TOKENS - SYSTEM_PROMPT_TOKENS - SAFETY_BUFFER

# Timeout and processing configuration
LAMBDA_TIMEOUT = 120  # seconds (leaving 5 seconds buffer from 30s default)
CHUNK_TIMEOUT = 90   # seconds per chunk
MAX_WORKERS = 3      # Maximum number of parallel chunk processing

# OpenAI API rate limiting
MAX_REQUESTS_PER_MINUTE = 3  # OpenAI's rate limit for GPT-4
RATE_LIMIT_WINDOW = 60       # Rate limit window in seconds
REQUEST_SEMAPHORE = Semaphore(MAX_REQUESTS_PER_MINUTE)

# Retry configuration
MAX_RETRIES = 3
RETRY_BACKOFF_FACTOR = 2
RETRY_STATUS_FORCELIST = [429, 500, 502, 503, 504]

class RateLimiter:
    def __init__(self, max_requests: int, time_window: int):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
        self.lock = asyncio.Lock()

    async def acquire(self):
        async with self.lock:
            now = time.time()
            # Remove old requests
            self.requests = [req_time for req_time in self.requests if now - req_time < self.time_window]
            
            if len(self.requests) >= self.max_requests:
                # Wait until the oldest request expires
                wait_time = self.requests[0] + self.time_window - now
                if wait_time > 0:
                    await asyncio.sleep(wait_time)
                self.requests = self.requests[1:]
            
            self.requests.append(now)

rate_limiter = RateLimiter(MAX_REQUESTS_PER_MINUTE, RATE_LIMIT_WINDOW)

def create_session_with_retries():
    """
    Create a requests session with retry logic.
    """
    session = requests.Session()
    retry_strategy = Retry(
        total=MAX_RETRIES,
        backoff_factor=RETRY_BACKOFF_FACTOR,
        status_forcelist=RETRY_STATUS_FORCELIST,
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session

def count_tokens(text: str) -> int:
    """
    Count the number of tokens in a text string using a simple estimation.
    Rough estimate: 1 token ≈ 0.75 words for English text.
    """
    # Simple estimation without tiktoken to avoid binary compatibility issues
    words = len(text.split())
    # GPT models typically use ~1.3 tokens per word for English
    return int(words * 1.3)

def split_transcript_into_chunks(transcript: str, max_tokens: int) -> List[str]:
    """
    Split a long transcript into chunks that fit within token limits.
    Tries to split at natural conversation boundaries.
    """
    # Split transcript into lines
    lines = transcript.split('\n')
    chunks = []
    current_chunk = []
    current_tokens = 0

    for line in lines:
        line_tokens = count_tokens(line)
        
        # If a single line is too long, we need to split it
        if line_tokens > max_tokens:
            # Split the long line into smaller parts
            words = line.split()
            temp_chunk = []
            temp_tokens = 0
            
            for word in words:
                word_tokens = count_tokens(word)
                if temp_tokens + word_tokens > max_tokens:
                    if temp_chunk:
                        chunks.append(' '.join(temp_chunk))
                    temp_chunk = [word]
                    temp_tokens = word_tokens
                else:
                    temp_chunk.append(word)
                    temp_tokens += word_tokens
            
            if temp_chunk:
                chunks.append(' '.join(temp_chunk))
            continue

        # If adding this line would exceed the limit, start a new chunk
        if current_tokens + line_tokens > max_tokens:
            if current_chunk:
                chunks.append('\n'.join(current_chunk))
            current_chunk = [line]
            current_tokens = line_tokens
        else:
            current_chunk.append(line)
            current_tokens += line_tokens

    # Add the last chunk if it exists
    if current_chunk:
        chunks.append('\n'.join(current_chunk))

    return chunks

async def call_llm_async(prompt: str, session: requests.Session) -> str:
    """
    Call the GPT-4 API with rate limiting.
    """
    if not API_KEY:
        raise ValueError("OPENAI_API_KEY environment variable is not set")

    if not API_KEY.startswith('sk-'):
        raise ValueError("OPENAI_API_KEY appears to be invalid (should start with 'sk-')")

    print(f"Using API key: {API_KEY[:10]}...{API_KEY[-4:]}")

    # Calculate max tokens based on prompt length
    max_tokens = calculate_max_tokens(prompt)

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}"
    }

    payload = {
        "model": "gpt-4o-mini",
        "messages": [
            {"role": "system", "content": "You are an expert sessioned therapist."},
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.5,
        "max_tokens": max_tokens
    }

    try:
        # Wait for rate limit
        await rate_limiter.acquire()

        # Make the API call
        print(f"Making API call to OpenAI with payload: {json.dumps(payload, indent=2)}")
        response = session.post(API_URL, headers=headers, json=payload, timeout=CHUNK_TIMEOUT)

        print(f"OpenAI API response status: {response.status_code}")
        print(f"OpenAI API response headers: {dict(response.headers)}")

        if response.status_code != 200:
            print(f"OpenAI API error response: {response.text}")

        response.raise_for_status()

        response_data = response.json()
        return response_data["choices"][0]["message"]["content"].strip()
    except requests.exceptions.HTTPError as e:
        print(f"HTTP Error in API call: {str(e)}")
        print(f"Response status code: {e.response.status_code}")
        print(f"Response text: {e.response.text}")
        raise
    except Exception as e:
        print(f"General error in API call: {str(e)}")
        raise

async def process_chunk_async(chunk: str, target_client_label: str, diagnosis_info: Optional[str], session: requests.Session) -> str:
    """
    Process a single chunk with rate limiting.
    """
    try:
        prompt = build_prompt(chunk, target_client_label, diagnosis_info)
        return await call_llm_async(prompt, session)
    except Exception as e:
        print(f"Error processing chunk: {str(e)}")
        return generate_fallback_response(chunk, target_client_label)

async def process_chunks_async(chunks: List[str], target_client_label: str, diagnosis_info: Optional[str]) -> List[str]:
    """
    Process multiple chunks with rate limiting.
    """
    session = create_session_with_retries()
    tasks = []

    # Create tasks for each chunk
    for chunk in chunks:
        task = asyncio.create_task(
            process_chunk_async(chunk, target_client_label, diagnosis_info, session)
        )
        tasks.append(task)

    # Wait for all tasks to complete
    chunk_notes = await asyncio.gather(*tasks, return_exceptions=True)

    # Handle any exceptions
    processed_notes = []
    for i, result in enumerate(chunk_notes):
        if isinstance(result, Exception):
            print(f"Error processing chunk {i}: {str(result)}")
            processed_notes.append(generate_fallback_response(chunks[i], target_client_label))
        else:
            processed_notes.append(result)

    return processed_notes

def combine_notes(notes: List[str]) -> str:
    """
    Combine multiple PIE notes into a single comprehensive note.
    """
    if not notes:
        return "No notes generated."

    if len(notes) == 1:
        return notes[0]

    # Extract P, I, E sections from each note
    sections = []
    for note in notes:
        try:
            p_section = note.split("I (Intervention):")[0].replace("P (Purpose and Problem):", "").strip()
            i_section = note.split("I (Intervention):")[1].split("E (Evaluation):")[0].strip()
            e_section = note.split("E (Evaluation):")[1].strip()
            sections.append((p_section, i_section, e_section))
        except:
            sections.append((note, "", ""))

    # Combine sections
    combined_p = " ".join(p for p, _, _ in sections if p)
    combined_i = " ".join(i for _, i, _ in sections if i)
    combined_e = " ".join(e for _, _, e in sections if e)

    return f"""P (Purpose and Problem): {combined_p}

I (Intervention): {combined_i}

E (Evaluation): {combined_e}"""

def build_prompt(transcript: str, target_client_label: str, diagnosis_info: Optional[str] = None) -> str:
    """
    Construct the prompt for GPT-4 to generate HIPAA-compliant PIE notes.
    """
    diagnosis_text = f" Diagnosis: {diagnosis_info}." if diagnosis_info else ""

    prompt = (
        f"You are an expert sessioned therapist with up-to-date knowledge of the DSM-5 and evidence-based practices. "
        f"Below is a transcript of a couples counseling session with role labels such as 'therapist', 'client1', 'client2', etc. "
        f"No names or identifying adjectives are present. Your task is to generate a HIPAA-compliant PIE note that meets the following documentation expectations:\n\n"
        f"P (Purpose and Problem): Clearly state the purpose of the session and the presenting problem, tied to the client's treatment plan goals. "
        f"Integrate the following diagnosis information into this section if provided:{diagnosis_text}\n\n"
        f"I (Intervention): Detail the evidence-based interventions performed during the session, including CBT-based techniques, "
        f"psychoeducation, or mindfulness interventions.\n\n"
        f"E (Evaluation): Evaluate the client's status (alertness, orientation, engagement), include any administered scales (e.g., PHQ-9, GAD-7) "
        f"with scores, describe the level of progress (none, minimal, minor, significant), and provide recommendations for follow-up.\n\n"
        f"Generate the PIE note for this session keeping in mind that is this the primary client '{target_client_label}'. Integrate any relevant context from other session participants, "
        f"while focusing primarily on the target client. Please refer to the client at all times as the client\n\n"
        f"Transcript:\n{transcript}\n\n"
        f"PIE Note:"
    )
    return prompt

def generate_fallback_response(transcript: str, target_client_label: str) -> str:
    """
    Generate a basic fallback response when the API call fails.
    """
    return (
        f"P (Purpose and Problem): Session focused on addressing concerns for {target_client_label}. "
        f"Client presented with issues discussed in the session.\n\n"
        f"I (Intervention): Therapist engaged in therapeutic conversation and provided support.\n\n"
        f"E (Evaluation): Client was present and engaged in the session. "
        f"Recommendation: Continue with scheduled follow-up sessions."
    )

def calculate_max_tokens(prompt: str) -> int:
    """
    Calculate the maximum tokens available for completion based on prompt length.
    """
    prompt_tokens = count_tokens(prompt)
    available_tokens = MAX_CONTEXT_LENGTH - prompt_tokens - SYSTEM_PROMPT_TOKENS - SAFETY_BUFFER

    # Ensure we stay within our defined limits
    return max(MIN_COMPLETION_TOKENS, min(available_tokens, MAX_COMPLETION_TOKENS))

def generate_pie_notes(transcript: str, target_client_label: str, diagnosis_info: Optional[str] = None) -> Dict[str, Any]:
    """
    Generate HIPAA-compliant PIE notes for the specified client using GPT-4.
    Handles long transcripts by splitting them into chunks and combining the results.
    """
    start_time = time.time()
    try:
        # Count total tokens in transcript
        total_tokens = count_tokens(transcript)

        # If transcript is within limits, process it directly
        if total_tokens <= MAX_TRANSCRIPT_TOKENS:
            session = create_session_with_retries()
            prompt = build_prompt(transcript, target_client_label, diagnosis_info)
            # Use asyncio to handle the single request
            pie_notes = asyncio.run(call_llm_async(prompt, session))
            chunks_processed = 1
        else:
            # Split transcript into chunks
            chunks = split_transcript_into_chunks(transcript, MAX_TRANSCRIPT_TOKENS)

            # Process chunks with rate limiting
            chunk_notes = asyncio.run(process_chunks_async(chunks, target_client_label, diagnosis_info))

            # Combine the notes
            pie_notes = combine_notes(chunk_notes)
            chunks_processed = len(chunks)

        processing_time = time.time() - start_time
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        return {
            "statusCode": 200,
            "body": {
                "timestamp": timestamp,
                "target_client": target_client_label,
                "pie_notes": pie_notes,
                "processing_info": {
                    "total_transcript_tokens": total_tokens,
                    "chunks_processed": chunks_processed,
                    "processing_time_seconds": round(processing_time, 2)
                }
            }
        }
    except Exception as e:
        # Ultimate fallback - return basic response
        return {
            "statusCode": 200,
            "body": {
                "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "target_client": target_client_label,
                "pie_notes": generate_fallback_response(transcript, target_client_label),
                "error": f"Note: Using fallback response due to error: {str(e)}",
                "processing_info": {
                    "processing_time_seconds": round(time.time() - start_time, 2)
                }
            }
        }

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    AWS Lambda handler function.
    """
    try:
        # Validate required parameters
        if not event.get('transcript'):
            return {
                "statusCode": 400,
                "body": {
                    "error": "transcript is required",
                    "fallback_notes": "Unable to generate notes: Missing transcript"
                }
            }

        if not event.get('target_client_label'):
            return {
                "statusCode": 400,
                "body": {
                    "error": "target_client_label is required",
                    "fallback_notes": "Unable to generate notes: Missing target client label"
                }
            }

        # Extract parameters
        transcript = event['transcript']
        target_client_label = event['target_client_label']
        diagnosis_info = event.get('diagnosis_info')  # Optional

        # Generate notes
        return generate_pie_notes(transcript, target_client_label, diagnosis_info)

    except Exception as e:
        # Ultimate fallback for any unexpected errors
        return {
            "statusCode": 200,  # Still return 200 to indicate we have a response
            "body": {
                "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "target_client": event.get('target_client_label', 'unknown'),
                "pie_notes": "Unable to generate detailed notes at this time. Please try again later.",
                "error": f"Note: Using fallback response due to error: {str(e)}"
            }
        }
