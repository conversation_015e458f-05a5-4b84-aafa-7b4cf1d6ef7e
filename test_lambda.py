#!/usr/bin/env python3
"""
Test script for the Lambda function
"""
import os
import json
from lambda_function import lambda_handler

def test_lambda_function():
    """Test the Lambda function with sample data"""
    
    # Sample test event
    test_event = {
        "transcript": """
        therapist: Good morning, how are you both feeling today?
        client1: I've been feeling really anxious about our communication issues.
        client2: I agree, we seem to argue about everything lately.
        therapist: Let's explore what's happening in your communication patterns.
        client1: I feel like client2 never listens to me.
        client2: That's not true! I do listen, but client1 always interrupts me.
        therapist: I can see both of you are feeling unheard. Let's practice some active listening techniques.
        """,
        "target_client_label": "client1",
        "diagnosis_info": "Generalized Anxiety Disorder"
    }
    
    # Mock context (not used in our function)
    class MockContext:
        def __init__(self):
            self.function_name = "test-function"
            self.memory_limit_in_mb = 128
            self.invoked_function_arn = "arn:aws:lambda:us-east-1:123456789012:function:test-function"
            self.aws_request_id = "test-request-id"
    
    context = MockContext()
    
    print("Testing Lambda function...")
    print(f"Input event: {json.dumps(test_event, indent=2)}")
    print("\n" + "="*50 + "\n")
    
    try:
        # Call the Lambda handler
        result = lambda_handler(test_event, context)
        
        print("Lambda function executed successfully!")
        print(f"Status Code: {result['statusCode']}")
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if result['statusCode'] == 200:
            print("\n✅ Test PASSED - Function returned success")
        else:
            print(f"\n❌ Test FAILED - Function returned error: {result['statusCode']}")
            
    except Exception as e:
        print(f"❌ Test FAILED - Exception occurred: {str(e)}")
        import traceback
        traceback.print_exc()

def test_missing_parameters():
    """Test the Lambda function with missing parameters"""
    print("\n" + "="*50)
    print("Testing with missing parameters...")
    
    # Test missing transcript
    test_event_no_transcript = {
        "target_client_label": "client1"
    }
    
    # Test missing target_client_label
    test_event_no_client = {
        "transcript": "Sample transcript"
    }
    
    class MockContext:
        pass
    
    context = MockContext()
    
    print("\nTest 1: Missing transcript")
    result1 = lambda_handler(test_event_no_transcript, context)
    print(f"Result: {json.dumps(result1, indent=2)}")
    
    print("\nTest 2: Missing target_client_label")
    result2 = lambda_handler(test_event_no_client, context)
    print(f"Result: {json.dumps(result2, indent=2)}")

if __name__ == "__main__":
    # Check if API key is set
    if not os.environ.get('OPENAI_API_KEY'):
        print("⚠️  WARNING: OPENAI_API_KEY environment variable is not set!")
        print("The function will use fallback responses.")
        print("To test with real API calls, set your OpenAI API key:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        print("\n" + "="*50 + "\n")
    
    # Run tests
    test_lambda_function()
    test_missing_parameters()
    
    print("\n" + "="*50)
    print("Testing completed!")
