# Family Therapy PIE Notes Generator - AWS Lambda Function

This repository contains an AWS Lambda function that generates HIPAA-compliant PIE (Purpose, Intervention, Evaluation) notes for family therapy sessions using OpenAI's GPT-4 API.

## Features

- **HIPAA-compliant PIE notes generation**
- **Long transcript handling** with automatic chunking
- **Rate limiting** for OpenAI API calls
- **Async processing** for better performance
- **Fallback responses** for error handling
- **Token counting** and management

## Setup

### 1. Environment Variables
Create a `.env` file based on `.env.example`:
```bash
OPENAI_API_KEY=your_openai_api_key_here
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Test Locally
```bash
python test_lambda.py
```

### 4. Deploy to AWS Lambda
```bash
./deploy.sh
```

## Usage

The Lambda function expects the following input:

```json
{
  "transcript": "Session transcript here...",
  "target_client_label": "client1",
  "diagnosis_info": "Optional diagnosis information"
}
```

## Response Format

```json
{
  "statusCode": 200,
  "body": {
    "timestamp": "2024-01-01 12:00:00",
    "target_client": "client1",
    "pie_notes": "Generated PIE notes...",
    "processing_info": {
      "total_transcript_tokens": 1500,
      "chunks_processed": 1,
      "processing_time_seconds": 5.2
    }
  }
}
```

## Configuration

- **MAX_CONTEXT_LENGTH**: 32000 tokens (GPT-4 limit)
- **MAX_REQUESTS_PER_MINUTE**: 3 (OpenAI rate limit)
- **LAMBDA_TIMEOUT**: 120 seconds
- **MAX_WORKERS**: 3 parallel chunk processing

## Troubleshooting

### Test OpenAI Connection
```bash
export OPENAI_API_KEY='your-api-key-here'
python3 test_openai_connection.py
```

### Common Issues

**500 Server Error from OpenAI**
- Check if your API key is valid and has credits
- Verify the model `gpt-4o-mini` is available for your account
- OpenAI might be experiencing temporary issues

**Authentication Error (401)**
- Verify your API key starts with `sk-`
- Check if the API key is correctly set in Lambda environment variables

**Rate Limit Error (429)**
- Your account has hit rate limits
- Wait a few minutes before trying again
- Consider upgrading your OpenAI plan

**Timeout Errors**
- Increase Lambda timeout to 2+ minutes
- Check your internet connectivity
- OpenAI API might be slow